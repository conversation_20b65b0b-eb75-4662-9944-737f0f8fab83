#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本 - 验证依赖包和配置是否正确
"""

import sys
import os

def test_python_version():
    """测试Python版本"""
    print("=== Python版本检查 ===")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✅ Python版本符合要求 (>= 3.7)")
        return True
    else:
        print("❌ Python版本过低，需要3.7或更高版本")
        return False

def test_dependencies():
    """测试依赖包安装"""
    print("\n=== 依赖包检查 ===")
    
    dependencies = [
        ('pandas', 'pd'),
        ('numpy', 'np'), 
        ('requests', 'requests'),
        ('openpyxl', 'openpyxl'),
        ('sklearn', 'sklearn'),
        ('langchain', 'langchain')
    ]
    
    failed_imports = []
    
    for package_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✅ {package_name} - 已安装")
        except ImportError:
            print(f"❌ {package_name} - 未安装")
            failed_imports.append(package_name)
    
    # 特别测试langchain的text_splitter
    try:
        from langchain.text_splitter import MarkdownTextSplitter
        print("✅ langchain.text_splitter - 已安装")
    except ImportError:
        print("❌ langchain.text_splitter - 未安装")
        failed_imports.append('langchain.text_splitter')
    
    # 特别测试sklearn的cosine_similarity
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        print("✅ sklearn.metrics.pairwise - 已安装")
    except ImportError:
        print("❌ sklearn.metrics.pairwise - 未安装")
        failed_imports.append('sklearn.metrics.pairwise')
    
    if failed_imports:
        print(f"\n❌ 以下依赖包需要安装: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖包已正确安装！")
        return True

def test_config_file():
    """测试配置文件"""
    print("\n=== 配置文件检查 ===")
    
    try:
        import config
        print("✅ config.py 文件存在")
        
        # 检查必要的配置项
        required_configs = [
            'EMBEDDING_API_ENDPOINT', 'EMBEDDING_API_KEY',
            'SUMMARIZER_API_ENDPOINT', 'SUMMARIZER_API_KEY',
            'EMBEDDING_MODEL', 'RERANKER_MODEL', 'SUMMARIZER_MODEL',
            'MD_FILE_PATH', 'XLSX_FILE_PATH'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                if value and not value.startswith('YOUR_') and not value.startswith('path/to/'):
                    print(f"✅ {config_name} - 已配置")
                else:
                    print(f"⚠️  {config_name} - 需要配置实际值")
                    missing_configs.append(config_name)
            else:
                print(f"❌ {config_name} - 配置项缺失")
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"\n⚠️  请在config.py中配置: {', '.join(missing_configs)}")
            return False
        else:
            print("\n✅ 配置文件检查通过！")
            return True
            
    except ImportError:
        print("❌ config.py 文件不存在或有语法错误")
        return False

def test_file_paths():
    """测试文件路径"""
    print("\n=== 文件路径检查 ===")
    
    try:
        import config
        
        # 检查MD文件
        if os.path.exists(config.MD_FILE_PATH):
            print(f"✅ MD文件存在: {config.MD_FILE_PATH}")
            md_exists = True
        else:
            print(f"❌ MD文件不存在: {config.MD_FILE_PATH}")
            md_exists = False
        
        # 检查Excel文件
        if os.path.exists(config.XLSX_FILE_PATH):
            print(f"✅ Excel文件存在: {config.XLSX_FILE_PATH}")
            xlsx_exists = True
        else:
            print(f"❌ Excel文件不存在: {config.XLSX_FILE_PATH}")
            xlsx_exists = False
        
        return md_exists and xlsx_exists
        
    except:
        print("❌ 无法检查文件路径，请先配置config.py")
        return False

def test_excel_format():
    """测试Excel文件格式"""
    print("\n=== Excel文件格式检查 ===")
    
    try:
        import config
        import pandas as pd
        
        if not os.path.exists(config.XLSX_FILE_PATH):
            print("❌ Excel文件不存在，跳过格式检查")
            return False
        
        df = pd.read_excel(config.XLSX_FILE_PATH)
        
        if '意见' in df.columns:
            print("✅ Excel文件包含'意见'列")
            print(f"   共有 {len(df)} 行数据")
            
            # 检查是否有空的意见
            empty_opinions = df['意见'].isna().sum()
            if empty_opinions > 0:
                print(f"⚠️  发现 {empty_opinions} 行空的意见")
            
            return True
        else:
            print("❌ Excel文件缺少'意见'列")
            print(f"   当前列名: {list(df.columns)}")
            return False
            
    except Exception as e:
        print(f"❌ Excel文件格式检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始环境测试...\n")
    
    tests = [
        ("Python版本", test_python_version),
        ("依赖包", test_dependencies),
        ("配置文件", test_config_file),
        ("文件路径", test_file_paths),
        ("Excel格式", test_excel_format)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*50)
    print("🔍 测试结果总结:")
    print("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！您可以开始使用文档处理工具了。")
        print("运行命令: python run_example.py")
    else:
        print("⚠️  部分测试失败，请根据上述提示进行修复。")
        print("参考文档: 安装使用指南.md")
    print("="*50)

if __name__ == "__main__":
    main()
