import sys
import json
import os
import logging
from pathlib import Path

import pandas as pd
import requests
import numpy as np
from langchain.text_splitter import MarkdownTextSplitter
from sklearn.metrics.pairwise import cosine_similarity

# --- Setup Logging ---
# Log to stderr to avoid interfering with stdout communication
logging.basicConfig(stream=sys.stderr, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# --- API Interaction Functions ---

def get_embedding(text, api_endpoint, api_key):
    """Calls the BAAI embedding API to get a vector for the given text."""
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    data = {"input": [text], "model": "bge-m3"}
    try:
        response = requests.post(f"{api_endpoint}/embeddings", headers=headers, json=data, timeout=30)
        response.raise_for_status()
        embedding = response.json()['data'][0]['embedding']
        return embedding
    except requests.exceptions.RequestException as e:
        logging.error(f"Embedding API request failed: {e}")
        raise

def get_reranked_results(query, docs, api_endpoint, api_key):
    """Calls the BAAI reranker API to get more accurate relevance scores."""
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    data = {
        "model": "bge-reranker-v2",
        "query": query,
        "documents": docs,
        "return_documents": True
    }
    try:
        response = requests.post(f"{api_endpoint}/rerank", headers=headers, json=data, timeout=30)
        response.raise_for_status()
        return response.json()['results']
    except requests.exceptions.RequestException as e:
        logging.error(f"Reranker API request failed: {e}")
        raise

def get_summary(text, api_endpoint, api_key):
    """Calls a generic LLM API to summarize the text."""
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    # Note: This payload structure is a guess and may need to be adapted
    # for the specific summarizer API (e.g., Kimi, DeepSeek).
    data = {
        "model": "summarizer-model", # Placeholder
        "messages": [
            {"role": "system", "content": "You are a helpful assistant that summarizes text. Condense the following content to under 400 tokens, capturing the key points."},
            {"role": "user", "content": text}
        ]
    }
    try:
        response = requests.post(f"{api_endpoint}/chat/completions", headers=headers, json=data, timeout=60)
        response.raise_for_status()
        # Adjust access to response based on actual API
        summary = response.json()['choices'][0]['message']['content']
        return summary
    except requests.exceptions.RequestException as e:
        logging.error(f"Summarizer API request failed: {e}")
        raise

# --- Core Processing Logic ---

def process_files(md_path, xlsx_path, baai_endpoint, baai_key, summarizer_endpoint, summarizer_key):
    """The main function to execute the document processing workflow."""
    logging.info("Starting file processing...")

    # 1. Load and chunk MD file
    logging.info(f"Loading MD file from: {md_path}")
    with open(md_path, 'r', encoding='utf-8') as f:
        md_content = f.read()

    text_splitter = MarkdownTextSplitter(chunk_size=10000, chunk_overlap=200)
    chunks = text_splitter.split_text(md_content)
    logging.info(f"Split MD file into {len(chunks)} chunks.")

    # 2. Get embeddings for all chunks
    logging.info("Getting embeddings for all MD chunks...")
    chunk_embeddings = [get_embedding(chunk, baai_endpoint, baai_key) for chunk in chunks]
    chunk_embeddings_np = np.array(chunk_embeddings)

    # 3. Load and process XLSX file
    logging.info(f"Loading XLSX file from: {xlsx_path}")
    df = pd.read_excel(xlsx_path)
    if '文本' not in df.columns:
        df['文本'] = "" # Add the column if it doesn't exist

    # 4. Loop through each opinion
    for index, row in df.iterrows():
        opinion = row['意见']
        logging.info(f"Processing opinion #{index + 1}: '{opinion[:50]}...'")

        try:
            # 5. Get opinion embedding
            opinion_embedding = get_embedding(opinion, baai_endpoint, baai_key)
            opinion_embedding_np = np.array([opinion_embedding])

            # 6. Cosine similarity for coarse retrieval (Top 5)
            similarities = cosine_similarity(opinion_embedding_np, chunk_embeddings_np)[0]
            top_5_indices = np.argsort(similarities)[-5:][::-1]
            top_5_chunks = [chunks[i] for i in top_5_indices]

            # 7. Rerank for fine-grained results
            reranked_results = get_reranked_results(opinion, top_5_chunks, baai_endpoint, baai_key)

            # 8. Check relevance and summarize
            if reranked_results and reranked_results[0]['relevance_score'] > 0.5: # Threshold
                most_relevant_chunk = reranked_results[0]['document']['text']
                summary = get_summary(most_relevant_chunk, summarizer_endpoint, summarizer_key)
                df.loc[index, '文本'] = summary
                logging.info(f"Found relevant content and summarized for opinion #{index + 1}.")
            else:
                df.loc[index, '文本'] = "未找到相关内容"
                logging.warning(f"No relevant content found for opinion #{index + 1}.")

        except Exception as e:
            logging.error(f"An error occurred while processing opinion #{index + 1}: {e}")
            df.loc[index, '文本'] = "处理时发生错误"

    # 9. Save updated XLSX file
    output_path = Path(xlsx_path)
    new_filename = f"{output_path.stem}_processed.xlsx"
    output_filepath = output_path.parent / new_filename
    df.to_excel(output_filepath, index=False)
    logging.info(f"Processing complete. Output saved to: {output_filepath}")

    return str(output_filepath)


# --- MCP Server Communication Loop ---

def send_response(response):
    """Sends a JSON response to stdout."""
    print(json.dumps(response), flush=True)

def main_loop():
    """Listens for incoming requests from stdin and processes them."""
    logging.info("MCP server 'document_processor' started. Waiting for requests.")
    for line in sys.stdin:
        try:
            request = json.loads(line)
            tool_name = request.get("tool")

            if tool_name == "run_processing":
                params = request.get("arguments", {})
                output_path = process_files(
                    md_path=params["md_file_path"],
                    xlsx_path=params["xlsx_file_path"],
                    baai_endpoint=params["baai_api_endpoint"],
                    baai_key=params["baai_api_key"],
                    summarizer_endpoint=params["summarizer_api_endpoint"],
                    summarizer_key=params["summarizer_api_key"]
                )
                send_response({
                    "id": request.get("id"),
                    "content": [{"type": "text", "text": f"Processing finished. File saved at: {output_path}"}]
                })
            else:
                send_response({
                    "id": request.get("id"),
                    "content": [{"type": "text", "text": f"Unknown tool: {tool_name}"}],
                    "isError": True
                })

        except json.JSONDecodeError:
            logging.error("Failed to decode JSON from stdin.")
            send_response({"error": "Invalid JSON input"})
        except Exception as e:
            logging.error(f"An unhandled exception occurred: {e}")
            send_response({"error": str(e), "isError": True})


if __name__ == "__main__":
    main_loop()