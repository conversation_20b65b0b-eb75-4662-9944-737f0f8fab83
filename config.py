#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 在这里设置API密钥和文件路径
"""

# API配置 (OpenAI格式 - 适用于硅基流动等平台)
API_ENDPOINT = "https://api.siliconflow.cn"  # API端点 (硅基流动)
API_KEY = "YOUR_API_KEY"  # 请替换为您的API密钥

# 模型配置 (可根据平台支持的模型进行调整)
EMBEDDING_MODEL = "BAAI/bge-m3"  # 向量化模型
RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"  # 重排序模型
SUMMARIZER_MODEL = "deepseek-chat"  # 摘要模型

# 文件路径配置
MD_FILE_PATH = "path/to/your/document.md"  # Markdown文件路径
XLSX_FILE_PATH = "path/to/your/opinions.xlsx"  # Excel文件路径

# 处理参数配置
CHUNK_SIZE = 10000  # 文本分块大小
CHUNK_OVERLAP = 200  # 分块重叠大小
TOP_K = 5  # 粗排时选择的候选数量
RELEVANCE_THRESHOLD = 0.5  # 相关性阈值

# 常用模型选项 (供参考)
"""
向量化模型选项:
- BAAI/bge-m3
- BAAI/bge-large-zh-v1.5
- text-embedding-ada-002 (OpenAI)

重排序模型选项:
- BAAI/bge-reranker-v2-m3
- BAAI/bge-reranker-large

摘要模型选项:
- deepseek-chat
- gpt-3.5-turbo
- gpt-4
- claude-3-haiku
- Qwen/Qwen2-7B-Instruct
"""
