@echo off
echo ========================================
echo 文档处理工具 - 依赖安装脚本
echo ========================================
echo.

echo 正在检查Python版本...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)
echo.

echo 正在升级pip...
python -m pip install --upgrade pip
echo.

echo 正在安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pip install pandas
pip install openpyxl  
pip install langchain
pip install numpy
pip install requests
pip install scikit-learn
pip install python-dotenv

echo.
echo ========================================
echo 安装完成！正在运行环境测试...
echo ========================================
echo.

python test_environment.py

echo.
echo ========================================
echo 安装脚本执行完毕
echo ========================================
echo 如果测试通过，您可以开始使用工具了
echo 运行命令: python run_example.py
echo.
pause
