# “文档关联与填充”自动化处理方案 (v2)

## 1. 核心目标
构建一个自动化工作流，该工作流能够：
1.  读取指定的 Markdown (`.md`) 文件和 Excel (`.xlsx`) 文件。
2.  对于 Excel 文件中的每一条“意见”，在 Markdown 文件中智能地搜索并定位最相关的内容。
3.  将找到的相关内容进行摘要（精确控制在400 Token左右）后，回填到 Excel 文件对应的“文本”列中。
4.  整个过程自动化，处理完所有“意见”后输出最终的 Excel 文件。

## 2. 技术选型与架构
采用以后端处理为核心，通过调用API解决本地计算资源不足的问题。

*   **语言:** Python 3.x
*   **核心库:**
    *   **文件处理:** `pandas`, `openpyxl`
    *   **文本分块:** `LangChain` 的 `MarkdownTextSplitter`
    *   **网络请求:** `requests` 或 `httpx`
    *   **数学计算:** `numpy`, `scikit-learn`
*   **核心逻辑 (API驱动):**
    *   **向量化 (Embedding):** 调用 **智源悟道 BAAI 的 `bge-m3` 模型API**。
    *   **粗排 (Initial Retrieval):** 使用`numpy`或`scikit-learn`进行高效的**余弦相似度**计算，筛选出Top-K个候选文本块。
    *   **精排 (Re-ranking):** 将粗排筛选出的候选文本块与原始“意见”一同提交给 **BAAI 的 `bge-reranker-v2` 模型API**。
    *   **摘要 (Summarization):** 调用一个**通用的大语言模型API**（如Kimi, DeepSeek等）进行摘要。
    *   **错误处理:** 若无相关内容，则填入“**未找到相关内容**”。

## 3. 工作流程

```mermaid
graph TD
    subgraph 开始
        A[用户提供 MD 和 XLSX 文件路径及API密钥]
    end

    subgraph 步骤一: MD文档预处理 (一次性)
        A --> B[1. 加载MD文件]
        B --> C[2. 按~10k Token分块]
        C --> D[3. 调用 bge-m3 API, 将所有文本块转换为向量]
        D --> E[4. 在内存中缓存所有文本块原文及其向量]
    end

    subgraph 步骤二: XLSX意见循环处理
        E --> F[5. 加载XLSX文件]
        F --> G{6. For 每一条'意见'}
        G -- 有下一条 --> H[7. 调用 bge-m3 API, 获取'意见'的向量]
        H --> I[8. 计算'意见'向量与所有MD块向量的余弦相似度]
        I --> J[9. 筛选出Top-K个最相似的MD块 (粗排)]
        J --> K[10. 调用 bge-reranker-v2 API, 对Top-K个MD块进行精排]
        K --> L{11. 找到最相关的文本块?}
        L -- 是 --> M[12. 调用大模型API, 对相关块内容进行摘要 (生成~400 token)]
        M --> O[14. 将摘要文本填入'文本'列]
        L -- 否 --> N[13. 设置文本为'未找到相关内容']
        N --> O
        O --> G
        G -- 处理完毕 --> P[15. 保存更新后的XLSX文件]
    end

    subgraph 结束
        P --> Q[完成, 返回最终文件路径]
    end
```

## 4. MCP工具定义与使用规范

我们将创建一个名为 `document_processor` 的MCP服务器，其中包含一个工具 `run_processing`。

**安装规范:**
提供一个 `requirements.txt` 文件，通过 `pip install -r requirements.txt` 安装所有依赖。

**使用规范 (调用示例):**
```xml
<use_mcp_tool>
  <server_name>document_processor</server_name>
  <tool_name>run_processing</tool_name>
  <arguments>
    {
      "md_file_path": "C:/path/to/your/document.md",
      "xlsx_file_path": "C:/path/to/your/opinions.xlsx",
      "baai_api_endpoint": "https://your-baai-api-endpoint.com",
      "baai_api_key": "YOUR_BAAI_API_KEY",
      "summarizer_api_endpoint": "https://your-summarizer-api-endpoint.com",
      "summarizer_api_key": "YOUR_SUMMARIZER_API_KEY"
    }
  </arguments>
</use_mcp_tool>