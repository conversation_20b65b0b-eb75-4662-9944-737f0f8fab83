#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档关联与填充处理工具
简化版本 - 可直接在IDLE或命令行中运行
"""

import os
import logging
from pathlib import Path
from typing import List, Optional

import pandas as pd
import requests
import numpy as np
from langchain.text_splitter import MarkdownTextSplitter
from sklearn.metrics.pairwise import cosine_similarity

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器类"""

    def __init__(self, api_endpoint: str, api_key: str, embedding_model: str, reranker_model: str, summarizer_model: str):
        """
        初始化处理器

        Args:
            api_endpoint: API端点 (OpenAI格式)
            api_key: API密钥
            embedding_model: 向量化模型名称 (如: BAAI/bge-m3)
            reranker_model: 重排序模型名称 (如: BAAI/bge-reranker-v2-m3)
            summarizer_model: 摘要模型名称 (如: deepseek-chat, gpt-3.5-turbo等)
        """
        self.api_endpoint = api_endpoint.rstrip('/')  # 移除末尾的斜杠
        self.api_key = api_key
        self.embedding_model = embedding_model
        self.reranker_model = reranker_model
        self.summarizer_model = summarizer_model
        
    def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示 (OpenAI格式)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "input": [text],
            "model": self.embedding_model
        }

        try:
            response = requests.post(f"{self.api_endpoint}/v1/embeddings",
                                   headers=headers, json=data, timeout=30)
            response.raise_for_status()
            embedding = response.json()['data'][0]['embedding']
            return embedding
        except requests.exceptions.RequestException as e:
            logger.error(f"Embedding API请求失败: {e}")
            raise
    
    def get_reranked_results(self, query: str, docs: List[str]) -> List[dict]:
        """使用重排序模型获取更准确的相关性评分 (OpenAI格式)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "model": self.reranker_model,
            "query": query,
            "documents": docs,
            "return_documents": True
        }

        try:
            response = requests.post(f"{self.api_endpoint}/v1/rerank",
                                   headers=headers, json=data, timeout=30)
            response.raise_for_status()
            return response.json()['results']
        except requests.exceptions.RequestException as e:
            logger.error(f"Reranker API请求失败: {e}")
            raise
    
    def get_summary(self, text: str) -> str:
        """调用大语言模型API生成摘要 (OpenAI格式)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "model": self.summarizer_model,
            "messages": [
                {"role": "system", "content": "你是一个专业的文档摘要助手。请将以下内容压缩到400个token以内，保留关键信息。"},
                {"role": "user", "content": text}
            ],
            "max_tokens": 500,
            "temperature": 0.3
        }

        try:
            response = requests.post(f"{self.api_endpoint}/v1/chat/completions",
                                   headers=headers, json=data, timeout=60)
            response.raise_for_status()
            summary = response.json()['choices'][0]['message']['content']
            return summary
        except requests.exceptions.RequestException as e:
            logger.error(f"摘要API请求失败: {e}")
            raise
    
    def process_documents(self, md_path: str, xlsx_path: str, 
                         chunk_size: int = 10000, chunk_overlap: int = 200,
                         top_k: int = 5, relevance_threshold: float = 0.5) -> str:
        """
        处理文档的主要方法
        
        Args:
            md_path: Markdown文件路径
            xlsx_path: Excel文件路径
            chunk_size: 文本分块大小
            chunk_overlap: 分块重叠大小
            top_k: 粗排时选择的top-k个候选
            relevance_threshold: 相关性阈值
            
        Returns:
            输出文件路径
        """
        logger.info("开始处理文档...")
        
        # 1. 加载并分块MD文件
        logger.info(f"加载MD文件: {md_path}")
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        text_splitter = MarkdownTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        chunks = text_splitter.split_text(md_content)
        logger.info(f"MD文件分为 {len(chunks)} 个文本块")
        
        # 2. 获取所有文本块的向量表示
        logger.info("正在获取所有文本块的向量表示...")
        chunk_embeddings = []
        for i, chunk in enumerate(chunks):
            logger.info(f"处理文本块 {i+1}/{len(chunks)}")
            embedding = self.get_embedding(chunk)
            chunk_embeddings.append(embedding)
        
        chunk_embeddings_np = np.array(chunk_embeddings)
        
        # 3. 加载Excel文件
        logger.info(f"加载Excel文件: {xlsx_path}")
        df = pd.read_excel(xlsx_path)
        if '文本' not in df.columns:
            df['文本'] = ""
        
        # 4. 处理每个意见
        total_opinions = len(df)
        for index, row in df.iterrows():
            opinion = row['意见']
            logger.info(f"处理意见 {index + 1}/{total_opinions}: '{opinion[:50]}...'")
            
            try:
                # 获取意见的向量表示
                opinion_embedding = self.get_embedding(opinion)
                opinion_embedding_np = np.array([opinion_embedding])
                
                # 计算余弦相似度进行粗排
                similarities = cosine_similarity(opinion_embedding_np, chunk_embeddings_np)[0]
                top_k_indices = np.argsort(similarities)[-top_k:][::-1]
                top_k_chunks = [chunks[i] for i in top_k_indices]
                
                # 使用重排序模型进行精排
                reranked_results = self.get_reranked_results(opinion, top_k_chunks)
                
                # 检查相关性并生成摘要
                if reranked_results and reranked_results[0]['relevance_score'] > relevance_threshold:
                    most_relevant_chunk = reranked_results[0]['document']['text']
                    summary = self.get_summary(most_relevant_chunk)
                    df.loc[index, '文本'] = summary
                    logger.info(f"为意见 {index + 1} 找到相关内容并生成摘要")
                else:
                    df.loc[index, '文本'] = "未找到相关内容"
                    logger.warning(f"意见 {index + 1} 未找到相关内容")
                    
            except Exception as e:
                logger.error(f"处理意见 {index + 1} 时发生错误: {e}")
                df.loc[index, '文本'] = "处理时发生错误"
        
        # 5. 保存结果
        output_path = Path(xlsx_path)
        new_filename = f"{output_path.stem}_processed.xlsx"
        output_filepath = output_path.parent / new_filename
        df.to_excel(output_filepath, index=False)
        logger.info(f"处理完成！结果保存至: {output_filepath}")
        
        return str(output_filepath)

def main():
    """主函数 - 可以在这里配置参数并运行"""

    # 配置API信息 (硅基流动等OpenAI格式API)
    API_ENDPOINT = "https://api.siliconflow.cn"  # 硅基流动API端点
    API_KEY = "YOUR_API_KEY"  # 请替换为实际的API密钥

    # 配置模型名称
    EMBEDDING_MODEL = "BAAI/bge-m3"  # 向量化模型
    RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"  # 重排序模型
    SUMMARIZER_MODEL = "deepseek-chat"  # 摘要模型 (可选: gpt-3.5-turbo, gpt-4等)

    # 配置文件路径
    MD_FILE_PATH = "path/to/your/document.md"  # 请替换为实际的MD文件路径
    XLSX_FILE_PATH = "path/to/your/opinions.xlsx"  # 请替换为实际的Excel文件路径

    # 创建处理器并运行
    processor = DocumentProcessor(
        api_endpoint=API_ENDPOINT,
        api_key=API_KEY,
        embedding_model=EMBEDDING_MODEL,
        reranker_model=RERANKER_MODEL,
        summarizer_model=SUMMARIZER_MODEL
    )

    try:
        output_file = processor.process_documents(MD_FILE_PATH, XLSX_FILE_PATH)
        print(f"处理完成！输出文件: {output_file}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
