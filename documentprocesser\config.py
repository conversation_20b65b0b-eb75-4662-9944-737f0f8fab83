#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 在这里设置API密钥和文件路径
"""

# API配置 (OpenAI格式 - 适用于硅基流动等平台)
# 向量化和重排序API (通常使用同一个端点)
EMBEDDING_API_ENDPOINT = "https://api.siliconflow.cn"  # 向量化API端点
EMBEDDING_API_KEY = "sk-snerzviylkickqyaoqlbirxetgojobhtzszjlbfptqzukmjn"  # 向量化API密钥

# 摘要API (可以使用不同的端点)
SUMMARIZER_API_ENDPOINT = "https://tbai.xin"  # 摘要API端点
SUMMARIZER_API_KEY = "sk-wYI2T063t30C8iMpO7MKhI1CuS4K7UUWIcrTnzJTBrsLqgdg"  # 摘要API密钥 (可以与向量化API相同或不同)

# 模型配置 (可根据平台支持的模型进行调整)
EMBEDDING_MODEL = "BAAI/bge-m3"  # 向量化模型
RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"  # 重排序模型
SUMMARIZER_MODEL = "gemini-2.5-pro"  # 摘要模型

# 文件路径配置
MD_FILE_PATH = r"‪D:\project\数据训练\test\guangzhoudadaobei.md"  # Markdown文件路径
XLSX_FILE_PATH = r"D:\project\数据训练\test\test_project.xlsx"  # Excel文件路径

# 处理参数配置
CHUNK_SIZE = 10000  # 文本分块大小
CHUNK_OVERLAP = 200  # 分块重叠大小
TOP_K = 5  # 粗排时选择的候选数量
RELEVANCE_THRESHOLD = 0.5  # 相关性阈值

# 常用模型选项 (供参考)
"""
向量化模型选项:
- BAAI/bge-m3
- BAAI/bge-large-zh-v1.5
- netease-youdao/bce-embedding-base_v1

重排序模型选项:
- BAAI/bge-reranker-v2-m3
- netease-youdao/bce-reranker-base_v1
- Qwen/Qwen3-Reranker-8B

摘要模型选项:
- gemini-2.5-pro-preview-06-05
- gemini-2.5-pro
- gpt-4.1
- gpt-4o
"""
