#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硅基流动配置示例 - 复制此文件为config.py并填入您的实际信息
"""

# 硅基流动API配置
EMBEDDING_API_ENDPOINT = "https://api.siliconflow.cn"  # 向量化API端点
EMBEDDING_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # 请替换为您的硅基流动API密钥

SUMMARIZER_API_ENDPOINT = "https://api.siliconflow.cn"  # 摘要API端点
SUMMARIZER_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # 摘要API密钥 (可以与向量化API相同或不同)

# 模型配置 (硅基流动支持的模型)
EMBEDDING_MODEL = "BAAI/bge-m3"  # 向量化模型
RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"  # 重排序模型
SUMMARIZER_MODEL = "deepseek-chat"  # 摘要模型

# 文件路径配置 (请替换为您的实际文件路径)
MD_FILE_PATH = r"C:\Users\<USER>\Documents\文档.md"  # Markdown文件路径
XLSX_FILE_PATH = r"C:\Users\<USER>\Documents\意见表.xlsx"  # Excel文件路径

# 处理参数配置
CHUNK_SIZE = 10000  # 文本分块大小
CHUNK_OVERLAP = 200  # 分块重叠大小
TOP_K = 5  # 粗排时选择的候选数量
RELEVANCE_THRESHOLD = 0.5  # 相关性阈值

# 硅基流动支持的模型列表 (供参考)
"""
=== 向量化模型 ===
- BAAI/bge-m3 (推荐)
- BAAI/bge-large-zh-v1.5
- BAAI/bge-base-zh-v1.5

=== 重排序模型 ===
- BAAI/bge-reranker-v2-m3 (推荐)
- BAAI/bge-reranker-large

=== 摘要模型 ===
- deepseek-chat (推荐，性价比高)
- Qwen/Qwen2-7B-Instruct
- Qwen/Qwen2-72B-Instruct
- meta-llama/Meta-Llama-3-8B-Instruct
- meta-llama/Meta-Llama-3-70B-Instruct

注意：
1. 请确保您的硅基流动账户有足够的余额
2. 不同模型的价格不同，请根据需要选择
3. 向量化和重排序模型建议使用BAAI系列
4. 摘要模型可以根据质量要求和成本考虑选择
"""

# 使用说明
"""
1. 将此文件复制为 config.py
2. 替换 API_KEY 为您的实际密钥
3. 替换文件路径为您的实际文件路径
4. 根据需要调整模型选择
5. 运行 python run_example.py
"""
