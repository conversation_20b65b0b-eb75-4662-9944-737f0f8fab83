# 文档关联与填充处理工具

一个简单易用的Python工具，用于自动化处理Markdown文件和Excel文件的关联与填充。

## 功能特点

- 🔍 **智能搜索**: 使用BAAI的bge-m3模型进行文本向量化和相似度计算
- 🎯 **精确匹配**: 使用bge-reranker-v2模型进行重排序，提高匹配精度
- 📝 **自动摘要**: 调用大语言模型API生成400token以内的精准摘要
- 📊 **批量处理**: 自动处理Excel文件中的所有意见条目
- 🚀 **简单易用**: 无需复杂配置，可直接在IDLE或命令行中运行

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 方法1: 使用配置文件

1. 编辑 `config.py` 文件，设置API密钥和文件路径：

```python
# API配置
BAAI_ENDPOINT = "https://your-baai-api-endpoint.com"
BAAI_KEY = "YOUR_BAAI_API_KEY"
SUMMARIZER_ENDPOINT = "https://your-summarizer-api-endpoint.com"
SUMMARIZER_KEY = "YOUR_SUMMARIZER_API_KEY"

# 文件路径
MD_FILE_PATH = "path/to/your/document.md"
XLSX_FILE_PATH = "path/to/your/opinions.xlsx"
```

2. 运行处理：

```python
python run_example.py
```

### 方法2: 在IDLE中交互式使用

```python
from document_processor_simple import DocumentProcessor

# 创建处理器
processor = DocumentProcessor(
    baai_endpoint="your_endpoint",
    baai_key="your_key", 
    summarizer_endpoint="your_endpoint",
    summarizer_key="your_key"
)

# 运行处理
output_file = processor.process_documents("document.md", "opinions.xlsx")
print(f"处理完成: {output_file}")
```

### 方法3: 交互式配置

```python
python run_example.py
# 选择 'n' 然后手动输入配置信息
```

## 文件说明

- `document_processor_simple.py`: 主要的处理器类
- `config.py`: 配置文件，设置API密钥和参数
- `run_example.py`: 使用示例和交互式运行脚本
- `requirements.txt`: Python依赖包列表

## 输入文件格式

### Excel文件要求
- 必须包含 `意见` 列
- 处理结果将填入 `文本` 列（如果不存在会自动创建）

### Markdown文件
- 标准的Markdown格式文档
- 工具会自动分块处理

## 输出

处理完成后会生成一个新的Excel文件，文件名为原文件名加上 `_processed` 后缀。

## 参数调整

可以在 `config.py` 中调整以下参数：

- `CHUNK_SIZE`: 文本分块大小（默认10000）
- `CHUNK_OVERLAP`: 分块重叠大小（默认200）
- `TOP_K`: 粗排候选数量（默认5）
- `RELEVANCE_THRESHOLD`: 相关性阈值（默认0.5）

## 注意事项

1. 确保API密钥有效且有足够的调用额度
2. 处理大文件时可能需要较长时间，请耐心等待
3. 如果某个意见找不到相关内容，会填入"未找到相关内容"
4. 如果处理过程中出错，会填入"处理时发生错误"

## 错误处理

工具包含完善的错误处理机制：
- API调用失败会记录详细错误信息
- 单个意见处理失败不会影响其他意见的处理
- 所有错误都会记录在日志中

## 许可证

MIT License
