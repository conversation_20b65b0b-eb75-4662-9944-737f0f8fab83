#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例 - 演示如何使用文档处理器
"""

from document_processor_simple import DocumentProcessor
import config

def run_processing():
    """运行文档处理"""

    # 创建处理器实例
    processor = DocumentProcessor(
        api_endpoint=config.API_ENDPOINT,
        api_key=config.API_KEY,
        embedding_model=config.EMBEDDING_MODEL,
        reranker_model=config.RERANKER_MODEL,
        summarizer_model=config.SUMMARIZER_MODEL
    )
    
    # 运行处理
    try:
        output_file = processor.process_documents(
            md_path=config.MD_FILE_PATH,
            xlsx_path=config.XLSX_FILE_PATH,
            chunk_size=config.CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP,
            top_k=config.TOP_K,
            relevance_threshold=config.RELEVANCE_THRESHOLD
        )
        print(f"✅ 处理完成！输出文件: {output_file}")
        return output_file
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        return None

def interactive_run():
    """交互式运行 - 可以在IDLE中使用"""
    print("=== 文档关联与填充处理工具 ===")
    print("请确保已在config.py中配置好API密钥和文件路径")
    
    # 询问用户是否要使用默认配置
    use_default = input("是否使用config.py中的默认配置？(y/n): ").lower().strip()
    
    if use_default == 'y':
        return run_processing()
    else:
        # 手动输入配置
        print("\n请输入配置信息:")
        api_endpoint = input("API端点 (如: https://api.siliconflow.cn): ").strip()
        api_key = input("API密钥: ").strip()
        embedding_model = input("向量化模型 (如: BAAI/bge-m3): ").strip()
        reranker_model = input("重排序模型 (如: BAAI/bge-reranker-v2-m3): ").strip()
        summarizer_model = input("摘要模型 (如: deepseek-chat): ").strip()
        md_path = input("Markdown文件路径: ").strip()
        xlsx_path = input("Excel文件路径: ").strip()

        # 创建处理器并运行
        processor = DocumentProcessor(
            api_endpoint=api_endpoint,
            api_key=api_key,
            embedding_model=embedding_model,
            reranker_model=reranker_model,
            summarizer_model=summarizer_model
        )
        
        try:
            output_file = processor.process_documents(md_path, xlsx_path)
            print(f"✅ 处理完成！输出文件: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ 处理过程中发生错误: {e}")
            return None

if __name__ == "__main__":
    interactive_run()
