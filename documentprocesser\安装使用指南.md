# 文档处理工具 - 完整安装使用指南

## 第一步：环境准备

### 1.1 确认Python版本
确保您的Python版本为3.7或更高：
```bash
python --version
```

### 1.2 创建虚拟环境（推荐）
```bash
# 创建虚拟环境
python -m venv document_processor_env

# 激活虚拟环境
# Windows:
document_processor_env\Scripts\activate
# macOS/Linux:
source document_processor_env/bin/activate
```

## 第二步：安装依赖

### 2.1 安装所有依赖包
在项目目录下运行：
```bash
pip install -r requirements.txt
```

### 2.2 如果上述命令失败，可以逐个安装：
```bash
pip install pandas
pip install openpyxl
pip install langchain
pip install numpy
pip install requests
pip install scikit-learn
pip install python-dotenv
```

### 2.3 验证安装
运行以下Python代码验证依赖是否正确安装：
```python
try:
    import pandas as pd
    import numpy as np
    import requests
    from langchain.text_splitter import MarkdownTextSplitter
    from sklearn.metrics.pairwise import cosine_similarity
    print("✅ 所有依赖包安装成功！")
except ImportError as e:
    print(f"❌ 依赖包安装失败: {e}")
```

## 第三步：配置API密钥

### 3.1 编辑config.py文件
打开 `config.py` 文件，替换以下内容：

**📝 API端点说明：**
- **EMBEDDING_API**: 用于向量化模型(BAAI/bge-m3)和重排序模型(BAAI/bge-reranker-v2-m3)
- **SUMMARIZER_API**: 用于摘要模型(deepseek-chat等)
- 两个API可以使用相同或不同的端点和密钥

```python
# 硅基流动API配置
EMBEDDING_API_ENDPOINT = "https://api.siliconflow.cn"  # 向量化+重排序API端点
EMBEDDING_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # 向量化+重排序API密钥

SUMMARIZER_API_ENDPOINT = "https://api.siliconflow.cn"  # 摘要API端点
SUMMARIZER_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # 摘要API密钥 (可以相同或不同)

# 模型配置
EMBEDDING_MODEL = "BAAI/bge-m3"  # 向量化模型 (使用EMBEDDING_API)
RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"  # 重排序模型 (使用EMBEDDING_API)
SUMMARIZER_MODEL = "deepseek-chat"  # 摘要模型 (使用SUMMARIZER_API)

# 文件路径配置 - 请替换为您的实际文件路径
MD_FILE_PATH = r"C:\Users\<USER>\Documents\文档.md"  # Markdown文件路径
XLSX_FILE_PATH = r"C:\Users\<USER>\Documents\意见表.xlsx"  # Excel文件路径
```

### 3.2 硅基流动API获取指南

**硅基流动 (推荐):**
1. 访问 https://siliconflow.cn/ 注册账号
2. 进入控制台，创建API密钥
3. 充值账户余额
4. API端点: https://api.siliconflow.cn

**其他OpenAI格式API平台:**
- DeepSeek API: https://platform.deepseek.com/
- Moonshot API: https://platform.moonshot.cn/
- 其他兼容OpenAI格式的API平台

## 第四步：准备数据文件

### 4.1 Excel文件格式要求
您的Excel文件必须包含以下列：
- `意见` 列：包含需要处理的意见文本
- `文本` 列：可选，如果不存在会自动创建，用于存放处理结果

示例Excel文件结构：
| 意见 | 文本 |
|------|------|
| 建议增加用户体验功能 | (处理后自动填入) |
| 希望优化系统性能 | (处理后自动填入) |

### 4.2 Markdown文件
准备一个包含相关内容的Markdown文档，工具会在其中搜索与意见相关的内容。

## 第五步：运行工具

### 方法1：使用配置文件运行（推荐）
```bash
python run_example.py
# 选择 'y' 使用默认配置
```

### 方法2：交互式配置运行
```bash
python run_example.py
# 选择 'n' 然后手动输入API密钥和文件路径
```

### 方法3：在IDLE中使用
打开Python IDLE，然后：
```python
# 导入模块
from document_processor_simple import DocumentProcessor

# 创建处理器（替换为您的实际API信息）
processor = DocumentProcessor(
    embedding_api_endpoint="https://api.siliconflow.cn",
    embedding_api_key="YOUR_EMBEDDING_API_KEY",
    summarizer_api_endpoint="https://api.siliconflow.cn",
    summarizer_api_key="YOUR_SUMMARIZER_API_KEY",
    embedding_model="BAAI/bge-m3",
    reranker_model="BAAI/bge-reranker-v2-m3",
    summarizer_model="deepseek-chat"
)

# 运行处理（替换为您的实际文件路径）
output_file = processor.process_documents(
    md_path="path/to/your/document.md",
    xlsx_path="path/to/your/opinions.xlsx"
)

print(f"处理完成: {output_file}")
```

## 第六步：查看结果

处理完成后，会在原Excel文件同目录下生成一个新文件，文件名为原文件名加上 `_processed` 后缀。

例如：`意见表.xlsx` → `意见表_processed.xlsx`

## 常见问题解决

### Q1: 依赖安装失败
**解决方案：**
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### Q2: API调用失败
**检查项目：**
- API密钥是否正确
- API端点地址是否正确
- 网络连接是否正常
- API额度是否充足

### Q3: 文件路径错误
**注意事项：**
- 使用绝对路径更可靠
- Windows路径使用反斜杠或双反斜杠
- 确保文件存在且有读取权限

### Q4: 处理速度慢
**优化建议：**
- 减少chunk_size参数
- 减少top_k参数
- 检查网络连接速度

## 参数调整指南

在 `config.py` 中可以调整以下参数：

```python
CHUNK_SIZE = 10000      # 文本分块大小，越大处理越慢但可能更准确
CHUNK_OVERLAP = 200     # 分块重叠大小，避免重要信息被截断
TOP_K = 5              # 粗排候选数量，越多越准确但处理越慢
RELEVANCE_THRESHOLD = 0.5  # 相关性阈值，越高要求越严格
```

## 技术支持

如果遇到问题，请检查：
1. Python版本是否兼容
2. 所有依赖是否正确安装
3. API配置是否正确
4. 文件路径是否存在
5. 网络连接是否正常

运行时的详细日志会显示在控制台，可以根据错误信息进行排查。
